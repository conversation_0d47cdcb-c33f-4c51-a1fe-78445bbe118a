<div class="single-team mb-40">
    <div class="team-thumb">
        <a href="<?php echo e($team->url); ?>">
            <div class="brd">
                <img src="<?php echo e(RvMedia::getImageUrl($team->photo, 'small', false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($team->title); ?>" />
            </div>
        </a>
    </div>
    <div class="team-info">
        <?php if($name = $team->name): ?>
            <h4><a href="<?php echo e($team->url); ?>"><?php echo e($name); ?></a></h4>
        <?php endif; ?>

        <?php if($title = $team->title): ?>
            <p><?php echo e($title); ?></p>
        <?php endif; ?>

        <?php if($socials = $team->socials): ?>
            <div class="team-social">
                <ul>
                    <?php $__currentLoopData = ['facebook', 'twitter', 'instagram']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($url = Arr::get($socials, $social)): ?>
                            <?php switch($social):
                                case ('twitter'): ?>
                                    <li>
                                        <a href="<?php echo e($url); ?>">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                    </li>
                                    <?php break; ?>

                                <?php case ('facebook'): ?>
                                    <li>
                                        <a href="<?php echo e($url); ?>">
                                            <i class="fab fa-facebook-f"></i>
                                        </a>
                                    </li>
                                    <?php break; ?>

                                <?php case ('instagram'): ?>
                                    <li>
                                        <a href="<?php echo e($url); ?>">
                                            <i class="fab fa-instagram"></i>
                                        </a>
                                    </li>
                                    <?php break; ?>

                            <?php endswitch; ?>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH G:\DL\app\app\platform\themes/riorelax/partials/shortcodes/teams/includes/item.blade.php ENDPATH**/ ?>