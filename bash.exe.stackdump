Stack trace:
Frame         Function      Args
0007FFFF7B30  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF6A30) msys-2.0.dll+0x1FE8E
0007FFFF7B30  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7E08) msys-2.0.dll+0x67F9
0007FFFF7B30  000210046832 (000210286019, 0007FFFF79E8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7B30  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF7B30  000210068E24 (0007FFFF7B40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF7E10  00021006A225 (0007FFFF7B40, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD5C800000 ntdll.dll
7FFD5B710000 KERNEL32.DLL
7FFD59F80000 KERNELBASE.dll
000050BA0000 sysfer.dll
7FFD5B200000 USER32.dll
7FFD59E90000 win32u.dll
7FFD5B3D0000 GDI32.dll
7FFD59950000 gdi32full.dll
7FFD5A370000 msvcp_win.dll
7FFD59D40000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD5C0C0000 advapi32.dll
7FFD5B660000 msvcrt.dll
7FFD5C180000 sechost.dll
7FFD5BA00000 RPCRT4.dll
7FFD59070000 CRYPTBASE.DLL
7FFD59CA0000 bcryptPrimitives.dll
7FFD5B020000 IMM32.DLL
